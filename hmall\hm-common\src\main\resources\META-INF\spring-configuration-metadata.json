{"groups": [{"name": "hm.db"}, {"name": "hm.mq"}, {"name": "hm.swagger"}, {"name": "hm.jwt", "type": "com.hmall.config.SecurityConfig", "sourceType": "com.hmall.config.JwtProperties"}, {"name": "hm.auth", "type": "com.hmall.config.MvcConfig", "sourceType": "com.hmall.config.AuthProperties"}], "properties": [{"name": "hm.mq.host", "type": "java.lang.String", "description": "rabbitmq的地址", "defaultValue": "***************"}, {"name": "hm.mq.port", "type": "java.lang.Integer", "description": "rabbitmq的端口", "defaultValue": "5672"}, {"name": "hm.mq.vhost", "type": "java.lang.String", "description": "rabbitmq的virtual-host地址", "defaultValue": "/hmxt"}, {"name": "hm.mq.username", "type": "java.lang.String", "description": "rabbitmq的用户名", "defaultValue": "hmxt"}, {"name": "hm.mq.password", "type": "java.lang.String", "description": "rabbitmq的密码", "defaultValue": "123321"}, {"name": "hm.mq.listener.retry.enable", "type": "java.lang.Bo<PERSON>an", "description": "是否开启rabbitmq的消费者重试机制", "defaultValue": "true"}, {"name": "hm.mq.listener.retry.interval", "type": "java.time.Duration", "description": "消费者重试初始失败等待时长", "defaultValue": "1000ms"}, {"name": "hm.mq.listener.retry.multiplier", "type": "java.lang.Integer", "description": "失败等待时长的递增倍数", "defaultValue": "1"}, {"name": "hm.mq.listener.retry.max-attempts", "type": "java.lang.Integer", "description": "消费者重试最大重试次数", "defaultValue": "3"}, {"name": "hm.mq.listener.retry.stateless", "type": "java.lang.Bo<PERSON>an", "description": "是否是无状态，默认true", "defaultValue": "true"}, {"name": "hm.db.host", "type": "java.lang.String", "description": "数据库地址", "defaultValue": "***************"}, {"name": "hm.db.port", "type": "java.lang.Integer", "description": "数据库端口", "defaultValue": "3306"}, {"name": "hm.db.database", "type": "java.lang.String", "description": "数据库database名", "defaultValue": ""}, {"name": "hm.db.un", "type": "java.lang.String", "description": "数据库用户名", "defaultValue": "root"}, {"name": "hm.db.pw", "type": "java.lang.String", "description": "数据库密码", "defaultValue": "123"}, {"name": "hm.swagger.title", "type": "java.lang.String", "description": "接口文档标题"}, {"name": "hm.swagger.description", "type": "java.lang.String", "description": "接口文档描述"}, {"name": "hm.swagger.email", "type": "java.lang.String", "description": "接口文档联系人邮箱"}, {"name": "hm.swagger.concat", "type": "java.lang.String", "description": "接口文档联系人"}, {"name": "hm.swagger.package", "type": "java.lang.String", "description": "接口controller扫描包"}, {"name": "hm.jwt.location", "type": "java.lang.String", "description": "秘钥存储地址"}, {"name": "hm.jwt.alias", "type": "java.lang.String", "description": "秘钥别名"}, {"name": "hm.jwt.password", "type": "java.lang.String", "description": "秘钥文件密码"}, {"name": "hm.jwt.tokenTTL", "type": "java.time.Duration", "description": "登录有效期"}, {"name": "hm.auth.excludePaths", "type": "java.util.List", "description": "登录放行的路径"}, {"name": "hm.auth.includePaths", "type": "java.util.List", "description": "登录拦截的路径"}], "hints": []}